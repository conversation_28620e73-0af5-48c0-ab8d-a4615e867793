<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Next Order Prediction System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>Next Order Prediction System</h1>
            <p class="subtitle">Predict and manage your inventory efficiently</p>
        </header>

        <div class="tab">
            <button class="tablinks" onclick="openTab(event, 'Process')">Process Dataset</button>
            <button class="tablinks" onclick="openTab(event, 'Output')">Get Output</button>
        </div>

        <div id="Process" class="tabcontent">
            <h2>Process Dataset</h2>
            <p>Process the dataset to generate predictions for all outlets based on historical data.</p>

            <div class="form-group">
                <label for="repid">Representative ID:</label>
                <input type="text" id="repid" name="repid" placeholder="Enter Rep ID" value="6713">
            </div>

            <button onclick="processDataset()">Process Dataset</button>

            <div class="spinner" id="processSpinner"></div>
            <div class="status-message" id="processMessage" style="display: none;"></div>
            <div id="processResult" class="result"></div>
            <div id="outletIdsSection" class="result" style="display:none;">
                <h3>Outlet IDs in Dataset:</h3>
                <ul id="outletIdsList"></ul>
            </div>
        </div>

        <div id="Output" class="tabcontent">
            <h2>Get Outlet Predictions</h2>
            <p>Retrieve the predicted order quantities for a specific outlet.</p>

            <div class="form-group">
                <label for="outletid">Outlet ID:</label>
                <select id="outletid" name="outletid">
                    <option value="">Select an Outlet ID</option>
                </select>
                <div class="spinner" id="outletSpinner" style="display: none;"></div>
            </div>

            <button onclick="getOutput()">Get Predictions</button>

            <div class="spinner" id="outputSpinner"></div>
            <div class="status-message" id="outputMessage" style="display: none;"></div>
            <div id="outputResult" class="result"></div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
