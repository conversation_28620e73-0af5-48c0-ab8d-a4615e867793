from flask import Flask, request, render_template, jsonify, abort, url_for
import pandas as pd
import os
import traceback

# Import custom modules
try:
    from next_order.data_handling.extract_data import extract_dataset
    from next_order.data_handling.clean_data import clean_dataset
    from next_order.prediction_and_output.prediction import prediction
    from next_order.prediction_and_output.output import output
    import database
except ImportError as e:
    print(f"Import error: {e}")
    traceback.print_exc()

# Initialize Flask app
app = Flask(__name__,
           template_folder='templates',
           static_folder='static')

# Global variables
repid = '6713'
visit_freq = 7

@app.route('/')
def read_root():
    return render_template('index.html')

# Processes a dataset and saves the predictions into a MySQL database.
@app.route('/process-dataset', methods=['POST'])
def process_data_and_save_result_in_database():
    try:
        # Use default repid if none provided
        repid = request.args.get('repid', '6713')
        csv_data = "E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/ranith_data_logs/ranith.csv"
        # Check if file exists
        if not os.path.exists(csv_data):
            return jsonify({"status": "error", "message": f"CSV file not found: {csv_data}"}), 404
        full_data = pd.read_csv(csv_data)
        db = database.get_connection()
        cursor = db.cursor()
        outlet_ids = list(full_data['customercode'].unique())
        for outlet_id, outlet_data in full_data.groupby("customercode"):
            predicted_data = []
            data = outlet_data
            extracted_dataset = extract_dataset(data, outlet_id)
            cleaned_data, ProductIDs, next_visit_date, outlier_product_ids = clean_dataset(extracted_dataset)
            predicted_df = prediction(ProductIDs, cleaned_data, next_visit_date, visit_freq)
            for __, row in predicted_df.iterrows():
                predicted_data.append((repid, outlet_id, row['ProductID'], row['predicted_qty']))
            cursor.executemany(
                "INSERT INTO suggested_order(repid,outletid,product,Qty) VALUES (%s, %s, %s, %s)",
                predicted_data
            )
            db.commit()
        db.close()
        return jsonify({"status": "success", "message": "Data processed and saved successfully!", "outlet_ids": outlet_ids})
    except Exception as e:
        print(f"Error in process_dataset: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

@app.route('/get-outlet-ids', methods=['GET'])
def get_outlet_ids():
    try:
        db = database.get_connection()
        cursor = db.cursor()
        cursor.execute("SELECT DISTINCT outletid FROM suggested_order ORDER BY outletid")
        result = cursor.fetchall()
        db.close()
        # Extract outlet IDs from the result tuples
        outlet_ids = [row[0] for row in result]
        return jsonify({"status": "success", "outlet_ids": outlet_ids})
    except Exception as e:
        print(f"Error in get_outlet_ids: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

@app.route('/output/<outletid>', methods=['POST'])
def get_output(outletid):
    try:
        db = database.get_connection()
        cursor = db.cursor()
        cursor.execute("SELECT product, Qty FROM suggested_order WHERE outletid = %s", (outletid,))
        result = cursor.fetchall()
        db.close()
        return jsonify({"status": "success", "data": result})
    except Exception as e:
        print(f"Error in get_output: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500


if __name__ == '__main__':
    print("Starting Flask application...")
    app.run(debug=True, host='127.0.0.1', port=6000)