'''import requests
from fastapi import FastAPI
import requests
import pandas as pd
import json

app = FastAPI()

API_URL = "http://sfa.phoenix.evisionmicroapps.com:60095/AnalyticsDataSource/Get?name=OrderApiData" 

@app.get("/convert_csv_to_json/")
def convert_csv_to_json():
    try:
        # Fetch the CSV data from the API
        response = requests.get(API_URL)
        response.raise_for_status()
        
        # Read CSV data into a Pandas DataFrame
        df = pd.read_csv(pd.compat.StringIO(response.text))
        
        # Convert DataFrame to JSON
        json_data = df.to_json(orient="records", indent=4)
        
        return {"json_output": json.loads(json_data)}
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
# from fastapi import FastAPI
# import requests
# import pandas as pd
# import json
# import io

# app = FastAPI()

# API_URL = "http://sfa.phoenix.evisionmicroapps.com:60095/AnalyticsDataSource/Get?name=OrderApiData"  

# @app.get("/convert_csv_to_json/")
# def convert_csv_to_json():
#     try:
#         # Fetch the CSV data from the API
#         response = requests.get(API_URL)
#         response.raise_for_status()

#         # Read CSV data into a Pandas DataFrame
#         df = pd.read_csv(io.StringIO(response.text))

#         # Convert DataFrame to JSON
#         json_data = df.to_json(orient="records", indent=4)

#         return {"json_output": json.loads(json_data)}
#     except Exception as e:
#         return {"error": str(e)}

# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)
from fastapi import FastAPI, Response
import requests
import pandas as pd
import json
import io

app = FastAPI()

API_URL = "http://sfa.phoenix.evisionmicroapps.com:60095/AnalyticsDataSource/Get?name=OrderApiData"  # Replace with your actual API URL

@app.get("/convert_csv_to_json/")
def convert_csv_to_json():
    try:
        # Fetch the CSV data from the API
        response = requests.get(API_URL)
        response.raise_for_status()

        # Read CSV data into a Pandas DataFrame
        df = pd.read_csv(io.StringIO(response.text))

        # Convert DataFrame to JSON
        json_data = df.to_json(orient="records", indent=4)
        
        return {"json_output": json.loads(json_data)}
    except Exception as e:
        return {"error": str(e)}

@app.get("/download_json/")
def download_json():
    try:
        # Fetch the CSV data from the API
        response = requests.get(API_URL)
        response.raise_for_status()
        
        # Read CSV data into a Pandas DataFrame
        df = pd.read_csv(io.StringIO(response.text))
        
        # Convert DataFrame to JSON
        json_data = df.to_json(orient="records", indent=4)
        
        return Response(content=json_data, media_type="application/json", headers={"Content-Disposition": "attachment; filename=data.json"})
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
