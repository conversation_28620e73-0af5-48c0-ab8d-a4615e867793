/* Main Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1000px;
    margin: 20px auto;
    background-color: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2.5rem;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin-top: 5px;
}

/* Tabs */
.tab {
    overflow: hidden;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    border-radius: 5px 5px 0 0;
    margin-bottom: 0;
}

.tab button {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 14px 20px;
    transition: 0.3s;
    font-size: 17px;
    color: #495057;
    font-weight: 500;
}

.tab button:hover {
    background-color: #e9ecef;
}

.tab button.active {
    background-color: #fff;
    border-bottom: 3px solid #3498db;
    color: #3498db;
}

/* Tab Content */
.tabcontent {
    display: none;
    padding: 20px;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 5px 5px;
    background-color: #fff;
    animation: fadeEffect 0.5s;
}

@keyframes fadeEffect {
    from {opacity: 0;}
    to {opacity: 1;}
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

input[type="text"], select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
    transition: border-color 0.3s;
    background-color: white;
}

input[type="text"]:focus, select:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

select option {
    padding: 8px 12px;
}

button {
    background-color: #3498db;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

/* Results */
.result {
    margin-top: 25px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    display: none;
}

.result h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: 600;
    color: #2c3e50;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
    display: none;
}

/* Small spinner for outlet loading */
#outletSpinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    margin: 10px auto 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Messages */
.status-message {
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Processing Details */
.processing-details {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    margin-top: 15px;
}

.processing-details h4 {
    margin-top: 15px;
    margin-bottom: 10px;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.processing-details p {
    margin: 10px 0;
}

.processing-details ul {
    margin: 10px 0;
    padding-left: 20px;
}

.processing-details li {
    margin-bottom: 5px;
}

.success-text {
    color: #28a745;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .tab button {
        padding: 10px 15px;
        font-size: 15px;
    }

    input[type="text"], button {
        padding: 10px;
    }
}
