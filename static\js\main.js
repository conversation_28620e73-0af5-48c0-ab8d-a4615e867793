// Tab functionality
function openTab(evt, tabName) {
    var i, tabcontent, tablinks;

    // Hide all tab content
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    // Remove active class from all tab buttons
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Show the current tab and add active class to the button
    document.getElementById(tabName).style.display = "block";
    evt.currentTarget.className += " active";
}

// Process dataset function
function processDataset() {
    const repid = document.getElementById('repid').value;
    if (!repid) {
        showMessage('Please enter a Rep ID', 'error');
        return;
    }

    // Show loading spinner
    document.getElementById('processSpinner').style.display = 'block';
    document.getElementById('processResult').style.display = 'none';
    document.getElementById('outletIdsSection').style.display = 'none';

    // Clear previous results
    document.getElementById('processMessage').innerHTML = '';
    document.getElementById('processMessage').style.display = 'none';

    // Make API call
    fetch('/process-dataset?repid=' + repid, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        // Hide spinner
        document.getElementById('processSpinner').style.display = 'none';

        // Show result
        document.getElementById('processResult').style.display = 'block';

        // Display status message
        if (data.status === 'success') {
            showMessage(data.message, 'success', 'processMessage');

            // Display processing details
            let resultHtml = '<h3>Processing Details</h3>';
            resultHtml += '<div class="processing-details">';
            resultHtml += `<p><strong>Rep ID:</strong> ${repid}</p>`;
            resultHtml += `<p><strong>Status:</strong> <span class="success-text">Success</span></p>`;
            resultHtml += `<p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>`;
            resultHtml += '</div>';
            document.getElementById('processResult').innerHTML = resultHtml;

            // Show outlet IDs if present
            if (data.outlet_ids && Array.isArray(data.outlet_ids)) {
                const outletIdsSection = document.getElementById('outletIdsSection');
                const outletIdsList = document.getElementById('outletIdsList');
                outletIdsList.innerHTML = '';
                data.outlet_ids.forEach(id => {
                    const li = document.createElement('li');
                    li.textContent = id;
                    outletIdsList.appendChild(li);
                });
                outletIdsSection.style.display = 'block';

                // Refresh the outlet IDs dropdown
                loadOutletIds();
            } else {
                document.getElementById('outletIdsSection').style.display = 'none';
            }
        } else {
            showMessage(data.message || 'An error occurred', 'error', 'processMessage');
            document.getElementById('processResult').innerHTML = '<p>Processing failed. Please try again.</p>';
            document.getElementById('outletIdsSection').style.display = 'none';
        }
    })
    .catch(error => {
        // Hide spinner
        document.getElementById('processSpinner').style.display = 'none';
        showMessage('Error: ' + error, 'error', 'processMessage');
        document.getElementById('processResult').innerHTML = '<p>Processing failed. Please try again.</p>';
        document.getElementById('outletIdsSection').style.display = 'none';
    });
}

// Get output function
function getOutput() {
    const outletid = document.getElementById('outletid').value;
    if (!outletid) {
        showMessage('Please select an Outlet ID', 'error');
        return;
    }

    // Show loading spinner
    document.getElementById('outputSpinner').style.display = 'block';
    document.getElementById('outputResult').style.display = 'none';

    // Clear previous results
    document.getElementById('outputMessage').innerHTML = '';
    document.getElementById('outputMessage').style.display = 'none';

    // Make API call
    fetch('/output/' + outletid, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        // Hide spinner
        document.getElementById('outputSpinner').style.display = 'none';

        // Show result
        document.getElementById('outputResult').style.display = 'block';

        if (data.status === 'success') {
            // Create table with results
            let resultHtml = '<h3>Results for Outlet ID: ' + outletid + '</h3>';

            if (data.data && data.data.length > 0) {
                resultHtml += '<table>';
                resultHtml += '<thead><tr><th>Product</th><th>Quantity</th></tr></thead>';
                resultHtml += '<tbody>';

                data.data.forEach(item => {
                    resultHtml += `<tr><td>${item[0]}</td><td>${item[1]}</td></tr>`;
                });

                resultHtml += '</tbody></table>';
            } else {
                resultHtml += '<p>No data found for this outlet.</p>';
            }

            document.getElementById('outputResult').innerHTML = resultHtml;
        } else {
            showMessage(data.message || 'An error occurred', 'error', 'outputMessage');
        }
    })
    .catch(error => {
        // Hide spinner
        document.getElementById('outputSpinner').style.display = 'none';

        // Show error
        showMessage('Error: ' + error, 'error', 'outputMessage');
    });
}

// Helper function to show messages
function showMessage(message, type, elementId = null) {
    const messageElement = elementId ? document.getElementById(elementId) : document.createElement('div');

    messageElement.className = 'status-message ' + type;
    messageElement.innerHTML = message;
    messageElement.style.display = 'block';

    if (!elementId) {
        // If no specific element ID provided, create a temporary message
        document.body.appendChild(messageElement);

        // Remove after 5 seconds
        setTimeout(() => {
            messageElement.remove();
        }, 5000);
    }
}

// Function to load outlet IDs
function loadOutletIds() {
    const outletSpinner = document.getElementById('outletSpinner');
    const outletSelect = document.getElementById('outletid');

    // Show loading spinner
    outletSpinner.style.display = 'block';

    // Clear existing options except the first one
    outletSelect.innerHTML = '<option value="">Select an Outlet ID</option>';

    // Fetch outlet IDs from the server
    fetch('/get-outlet-ids', {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // Hide spinner
        outletSpinner.style.display = 'none';

        if (data.status === 'success' && data.outlet_ids) {
            // Populate the dropdown with outlet IDs
            data.outlet_ids.forEach(outletId => {
                const option = document.createElement('option');
                option.value = outletId;
                option.textContent = outletId;
                outletSelect.appendChild(option);
            });

            // Enable the select if it was disabled
            outletSelect.disabled = false;
        } else {
            // Show error message
            showMessage('Failed to load outlet IDs: ' + (data.message || 'Unknown error'), 'error');
            outletSelect.disabled = true;
        }
    })
    .catch(error => {
        // Hide spinner
        outletSpinner.style.display = 'none';

        // Show error message
        showMessage('Error loading outlet IDs: ' + error, 'error');
        outletSelect.disabled = true;
    });
}

// Initialize the first tab as active when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set the first tab as active by default
    document.getElementsByClassName('tablinks')[0].click();

    // Load outlet IDs when the page loads
    loadOutletIds();
});
